# ================================
# BUILD SYSTEM
# ================================
[build-system]
requires = ["setuptools>=68.0", "wheel"]
build-backend = "setuptools.build_meta"

# ================================
# PROJECT METADATA
# ================================
[project]
name = "dr-smith-langgraph"
version = "1.0.0"
description = "Unified LangGraph medical consultation system for <PERSON><PERSON> <PERSON>"
readme = "README.md"
requires-python = ">=3.10"

# ================================
# CORE DEPENDENCIES ONLY
# ================================
dependencies = [
    # LangChain ecosystem
    "langchain>=0.3.0,<0.4.0",
    "langgraph>=0.6.0,<0.7.0",
    "langchain-openai>=0.0.8,<1.0.0",

    # OpenAI client
    "openai>=1.12.0,<2.0.0",

    # Data validation
    "pydantic>=2.7.0,<3.0.0",

    # Environment variables
    "python-dotenv>=1.0.0,<2.0.0",

    # CLI interface
    "click>=8.1.0,<9.0.0",
]

# ================================
# OPTIONAL DEVELOPMENT DEPS
# ================================
[project.optional-dependencies]
dev = [
    "pytest>=8.0.0",
    "ruff>=0.4.0",
]

# ================================
# CLI ENTRY POINT
# ================================
[project.scripts]
dr-smith = "dr_smith_langgraph.cli:main"

# ================================
# PACKAGE DISCOVERY
# ================================
[tool.setuptools.packages.find]
where = ["src"]
include = ["dr_smith_langgraph*"]

# ================================
# BASIC TOOL CONFIG
# ================================
[tool.ruff]
line-length = 100
target-version = "py310"

[tool.pytest.ini_options]
testpaths = ["tests"]
