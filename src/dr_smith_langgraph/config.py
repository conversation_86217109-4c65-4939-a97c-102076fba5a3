from __future__ import annotations
import os
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# Load environment variables from .env file if present
load_dotenv()

# ================================
# CORE CONFIGURATION
# ================================

# Mandatory API configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise RuntimeError(
        "OPENAI_API_KEY is required. Please set it in your .env file or environment variables.\n"
        "Get your API key from: https://platform.openai.com/account/api-keys"
    )

# LLM Configuration
MODEL_NAME = os.getenv("MODEL_NAME", "gpt-4o-mini")  # Cost-effective default
TEMPERATURE = float(os.getenv("TEMPERATURE", "0.3"))  # Slightly higher for medical empathy
MAX_TOKENS = int(os.getenv("MAX_TOKENS", "2048"))  # Reasonable response length
REQUEST_TIMEOUT = int(os.getenv("REQUEST_TIMEOUT", "60"))  # 60 second timeout

# ================================
# GUSTORX INTEGRATION
# ================================

# Gustorx referral URLs for each specialty
GUSTORX_URLS = {
    "hair_loss": "https://app.gustorx.com/start-hl-consult",
    "erectile_dysfunction": "https://app.gustorx.com/start-ed-consult",
    "weight_loss": "https://app.gustorx.com/start-wl-consult"
}

# ================================
# UNIFIED AGENT CONFIGURATION
# ================================

# Tool usage preferences
TOOL_CONFIG = {
    "symptom_extraction": {
        "enabled": os.getenv("ENABLE_SYMPTOM_EXTRACTION", "true").lower() == "true",
        "auto_trigger_keywords": ["pain", "symptoms", "feel", "experiencing", "problem", "issue"],
        "temperature": 0.1  # Low temperature for structured extraction
    },
    "psychological_assessment": {
        "enabled": os.getenv("ENABLE_PSYCHOLOGICAL_SUPPORT", "true").lower() == "true",
        "auto_trigger_keywords": ["embarrassed", "ashamed", "worried", "anxious", "depressed", "frustrated"],
        "temperature": 0.4  # Medium temperature for emotional understanding
    },
    "safety_compliance": {
        "enabled": os.getenv("ENABLE_SAFETY_CHECKS", "true").lower() == "true",
        "strict_mode": os.getenv("STRICT_SAFETY_MODE", "true").lower() == "true",
        "emergency_keywords": ["suicide", "harm", "emergency", "chest pain", "difficulty breathing"],
        "temperature": 0.1  # Very low temperature for safety decisions
    },
    "language_adjustment": {
        "enabled": os.getenv("ENABLE_LANGUAGE_ADJUSTMENT", "true").lower() == "true",
        "default_complexity": os.getenv("DEFAULT_LANGUAGE_COMPLEXITY", "simple"),  # simple, moderate, technical
        "temperature": 0.2
    },
    "referral_resources": {
        "enabled": os.getenv("ENABLE_REFERRALS", "true").lower() == "true",
        "auto_suggest": os.getenv("AUTO_SUGGEST_REFERRALS", "true").lower() == "true",
        "temperature": 0.3
    }
}

# ================================
# SPECIALTY CONFIGURATION
# ================================

# Specialty detection sensitivity
SPECIALTY_DETECTION = {
    "confidence_threshold": float(os.getenv("SPECIALTY_CONFIDENCE_THRESHOLD", "0.7")),
    "enable_multi_specialty": os.getenv("ENABLE_MULTI_SPECIALTY", "true").lower() == "true",
    "default_specialty": os.getenv("DEFAULT_SPECIALTY", "GENERAL")
}

# Specialty-specific configurations
SPECIALTY_CONFIG = {
    "ERECTILE_DYSFUNCTION": {
        "requires_psychological_support": True,
        "sensitive_topic": True,
        "referral_threshold": "moderate",  # when to suggest Gustorx
        "common_comorbidities": ["cardiovascular", "diabetes", "psychological"]
    },
    "HAIR_HEALTH": {
        "requires_psychological_support": True,
        "sensitive_topic": True,
        "referral_threshold": "mild",
        "assessment_factors": ["pattern", "family_history", "hormones", "stress"]
    },
    "WEIGHT_LOSS": {
        "requires_psychological_support": True,
        "sensitive_topic": True,
        "referral_threshold": "moderate",
        "holistic_approach": True,
        "focus_areas": ["nutrition", "exercise", "behavior", "medical"]
    },
    "GENERAL": {
        "requires_psychological_support": False,
        "sensitive_topic": False,
        "referral_threshold": "high",
        "broad_assessment": True
    }
}

# ================================
# SYSTEM BEHAVIOR CONFIGURATION
# ================================

# Conversation management
CONVERSATION_CONFIG = {
    "max_context_messages": int(os.getenv("MAX_CONTEXT_MESSAGES", "20")),
    "enable_conversation_summary": os.getenv("ENABLE_CONVERSATION_SUMMARY", "true").lower() == "true",
    "summary_trigger_length": int(os.getenv("SUMMARY_TRIGGER_LENGTH", "10")),  # messages before summarizing
    "enable_session_continuity": os.getenv("ENABLE_SESSION_CONTINUITY", "true").lower() == "true"
}

# Response quality settings
RESPONSE_CONFIG = {
    "min_response_length": int(os.getenv("MIN_RESPONSE_LENGTH", "50")),
    "max_response_length": int(os.getenv("MAX_RESPONSE_LENGTH", "1500")),
    "enable_response_validation": os.getenv("ENABLE_RESPONSE_VALIDATION", "true").lower() == "true",
    "require_empathy_for_sensitive": os.getenv("REQUIRE_EMPATHY_FOR_SENSITIVE", "true").lower() == "true"
}

# ================================
# DEVELOPMENT & DEBUGGING
# ================================

# Development settings
DEBUG_CONFIG = {
    "enable_debug_logging": os.getenv("DEBUG_LOGGING", "false").lower() == "true",
    "log_tool_usage": os.getenv("LOG_TOOL_USAGE", "false").lower() == "true",
    "log_response_times": os.getenv("LOG_RESPONSE_TIMES", "false").lower() == "true",
    "enable_trace_mode": os.getenv("ENABLE_TRACE_MODE", "false").lower() == "true"
}

# Testing configurations
TEST_CONFIG = {
    "use_mock_llm": os.getenv("USE_MOCK_LLM_FOR_TESTS", "false").lower() == "true",
    "mock_response_delay": float(os.getenv("MOCK_RESPONSE_DELAY", "0.5")),
    "enable_integration_tests": os.getenv("ENABLE_INTEGRATION_TESTS", "true").lower() == "true"
}

# ================================
# PATHS AND DIRECTORIES
# ================================

# Project paths
BASE_DIR = Path(__file__).resolve().parent.parent
PROJECT_ROOT = BASE_DIR.parent
CONFIG_DIR = PROJECT_ROOT / "config"
LOGS_DIR = PROJECT_ROOT / "logs"
CACHE_DIR = PROJECT_ROOT / ".cache"

# Ensure directories exist
for directory in [LOGS_DIR, CACHE_DIR]:
    directory.mkdir(exist_ok=True)

# ================================
# UTILITY FUNCTIONS
# ================================

def get_llm_config() -> Dict[str, Any]:
    """Get the standard LLM configuration dictionary."""
    return {
        "openai_api_key": OPENAI_API_KEY,
        "model_name": MODEL_NAME,
        "temperature": TEMPERATURE,
        "max_tokens": MAX_TOKENS,
        "request_timeout": REQUEST_TIMEOUT
    }

def get_tool_config(tool_name: str) -> Optional[Dict[str, Any]]:
    """Get configuration for a specific tool."""
    return TOOL_CONFIG.get(tool_name)

def is_tool_enabled(tool_name: str) -> bool:
    """Check if a specific tool is enabled."""
    tool_config = TOOL_CONFIG.get(tool_name, {})
    return tool_config.get("enabled", True)

def get_specialty_config(specialty: str) -> Dict[str, Any]:
    """Get configuration for a specific medical specialty."""
    return SPECIALTY_CONFIG.get(specialty, SPECIALTY_CONFIG["GENERAL"])

def should_trigger_psychological_support(specialty: str) -> bool:
    """Determine if psychological support should be triggered for a specialty."""
    config = get_specialty_config(specialty)
    return config.get("requires_psychological_support", False)

def is_sensitive_specialty(specialty: str) -> bool:
    """Check if a specialty deals with sensitive topics."""
    config = get_specialty_config(specialty)
    return config.get("sensitive_topic", False)

def get_referral_url(specialty: str) -> Optional[str]:
    """Get the appropriate Gustorx referral URL for a specialty."""
    specialty_mapping = {
        "ERECTILE_DYSFUNCTION": "erectile_dysfunction",
        "HAIR_HEALTH": "hair_loss",
        "WEIGHT_LOSS": "weight_loss"
    }

    gustorx_key = specialty_mapping.get(specialty)
    return GUSTORX_URLS.get(gustorx_key) if gustorx_key else None

def validate_configuration() -> bool:
    """Validate that all required configuration is present and valid."""
    try:
        # Check API key
        if not OPENAI_API_KEY:
            raise RuntimeError("OPENAI_API_KEY is required")
        if not OPENAI_API_KEY.startswith('sk-'):
            raise RuntimeError("Invalid OpenAI API key format")

        # Check temperature range
        if not 0.0 <= TEMPERATURE <= 2.0:
            raise ValueError("Temperature must be between 0.0 and 2.0")

        # Check max tokens
        if MAX_TOKENS < 100 or MAX_TOKENS > 4096:
            raise ValueError("Max tokens must be between 100 and 4096")

        return True

    except Exception as e:
        raise RuntimeError(f"Configuration validation failed: {e}")  # Always raise RuntimeError

def get_environment_info() -> Dict[str, str]:
    """Get information about the current environment setup."""
    return {
        "model": MODEL_NAME,
        "temperature": str(TEMPERATURE),
        "tools_enabled": str(sum(1 for tool in TOOL_CONFIG.values() if tool.get("enabled", True))),
        "debug_mode": str(DEBUG_CONFIG["enable_debug_logging"]),
        "project_root": str(PROJECT_ROOT)
    }

# ================================
# CONFIGURATION VALIDATION
# ================================

# Validate configuration on import
if not validate_configuration():
    raise RuntimeError("Configuration validation failed. Please check your settings.")

# Export commonly used configurations
__all__ = [
    "OPENAI_API_KEY",
    "MODEL_NAME",
    "TEMPERATURE",
    "MAX_TOKENS",
    "GUSTORX_URLS",
    "TOOL_CONFIG",
    "SPECIALTY_CONFIG",
    "get_llm_config",
    "get_tool_config",
    "is_tool_enabled",
    "get_specialty_config",
    "should_trigger_psychological_support",
    "is_sensitive_specialty",
    "get_referral_url",
    "BASE_DIR",
    "PROJECT_ROOT"
]
