from __future__ import annotations
from typing import Optional
from langgraph.graph import StateGraph, MessagesState, START, END
from langgraph.types import Command
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from .agents import llm

from .agents import (
    unified_doctor_agent,
    classify_medical_specialty,
    extract_user_messages,
    get_conversation_summary
)

# ================================
# STATE MANAGEMENT
# ================================

class UnifiedConsultationState(MessagesState):
    """Simplified state for unified agent consultation."""
    current_specialty: str = "GENERAL"
    consultation_complete: bool = False
    session_metadata: dict = {}

# ================================
# NODE FUNCTIONS
# ================================

def unified_doctor_node(state: UnifiedConsultationState) -> Command:
    """
    Main consultation node using the unified <PERSON><PERSON> agent with all tools.
    This single node handles the entire consultation process.
    """
    try:
        # Get current messages
        messages = state.get("messages", [])

        if not messages:
            # No messages yet, shouldn't happen but handle gracefully
            welcome_msg = AIMessage(content="Hello! I'm Dr. Smith. How can I help you today?")
            return Command(
                update={"messages": messages + [welcome_msg]},
                goto=END
            )

        # Extract user context for metadata
        latest_user_msg = None
        for msg in reversed(messages):
            if msg.type == "human":
                latest_user_msg = msg.content
                break

        if latest_user_msg:
            # Update specialty classification based on latest input
            detected_specialty = classify_medical_specialty(latest_user_msg)
            state["current_specialty"] = detected_specialty

        # Prepare enhanced state for the unified agent
        enhanced_state = {
            "messages": messages,
            "current_specialty": state.get("current_specialty", "GENERAL"),
            "user_context": extract_user_messages(messages),
            "conversation_summary": get_conversation_summary(messages)
        }

        # Invoke the unified agent with full conversation context
        response = unified_doctor_agent.invoke(enhanced_state)

        # Extract the final message from the agent response
        if "messages" in response and response["messages"]:
            final_messages = response["messages"]

            # **FIX: Handle empty content in final message**
            if final_messages and (not final_messages[-1].content or final_messages[-1].content.strip() == ""):
                # Agent made tool calls but didn't generate final response
                # Force agent to provide a response based on the conversation
                fallback_prompt = f"""
Based on the user's concern: "{latest_user_msg}"
And the specialty: {state.get("current_specialty", "GENERAL")}

Please provide a comprehensive, empathetic response addressing their health concern.
"""
                fallback_response = llm.invoke([
                    SystemMessage(content="You are Dr. Smith. Provide a helpful medical consultation response."),
                    HumanMessage(content=fallback_prompt)
                ])

                # Replace empty message with actual response
                final_messages[-1] = AIMessage(content=fallback_response.content)

            # Update session metadata
            session_metadata = state.get("session_metadata", {})
            session_metadata.update({
                "last_specialty": state.get("current_specialty", "GENERAL"),
                "total_exchanges": len([msg for msg in final_messages if msg.type == "human"]),
                "response_generated": True
            })

            return Command(
                update={
                    "messages": final_messages,
                    "current_specialty": state.get("current_specialty", "GENERAL"),
                    "session_metadata": session_metadata,
                    "consultation_complete": False
                },
                goto=END
            )
        else:
            # Fallback if no proper response at all
            error_msg = AIMessage(content="I apologize, but I'm having trouble processing your request. Could you please rephrase your concern?")
            return Command(
                update={"messages": messages + [error_msg]},
                goto=END
            )

    except Exception as e:
        # Error handling with meaningful response
        error_msg = AIMessage(content=f"I understand your concern about your health issue. Let me help you differently. Could you please describe your concern again?")
        return Command(
            update={
                "messages": state.get("messages", []) + [error_msg],
                "session_metadata": {"error_handled": True}
            },
            goto=END
        )

def session_initializer_node(state: UnifiedConsultationState) -> Command:
    """
    Optional initialization node for new sessions.
    Sets up the consultation context and provides a warm greeting.
    """
    messages = state.get("messages", [])

    # If this is the very first interaction (no messages)
    if not messages:
        greeting = AIMessage(content="""👋 Hello! I'm Dr. Smith, and I'm here to help with your health concerns.

I specialize in:
• Erectile Dysfunction and sexual health
• Hair health and hair loss
• Weight management and metabolic health
• General medical guidance

I have access to specialized tools to provide you with comprehensive, empathetic care. Please feel free to share what's on your mind - I'm here to listen and help.

What health concern would you like to discuss today?""")

        return Command(
            update={
                "messages": [greeting],
                "current_specialty": "GENERAL",
                "session_metadata": {"session_started": True}
            },
            goto="unified_doctor"
        )
    else:
        # Continue with existing conversation
        return Command(goto="unified_doctor")

# ================================
# GRAPH CONSTRUCTION
# ================================

def build_graph() -> StateGraph:
    """
    Build the simplified consultation graph with unified agent.

    Flow: Entry -> Session Init -> Unified Doctor -> End
          (with loop back for multi-turn conversations)
    """
    graph = StateGraph(UnifiedConsultationState)

    # Add nodes
    graph.add_node("session_init", session_initializer_node)
    graph.add_node("unified_doctor", unified_doctor_node)

    # Set entry point
    graph.set_entry_point("session_init")

    # Define edges
    graph.add_edge("session_init", "unified_doctor")
    graph.add_edge("unified_doctor", END)

    return graph

def build_continuous_graph() -> StateGraph:
    """
    Build a continuous conversation graph that loops for multi-turn interactions.
    This version keeps the conversation going until explicitly ended.
    """
    graph = StateGraph(UnifiedConsultationState)

    # Add nodes
    graph.add_node("unified_doctor", unified_doctor_node)

    # Set entry point directly to the doctor
    graph.set_entry_point("unified_doctor")

    # Self-loop for continuous conversation
    # In practice, the CLI will handle the looping by calling invoke repeatedly
    graph.add_edge("unified_doctor", END)

    return graph

# ================================
# HELPER FUNCTIONS
# ================================

def create_initial_state(user_message: str) -> dict:
    """Create an initial state with the user's first message."""
    return UnifiedConsultationState(
        messages=[HumanMessage(content=user_message)],
        current_specialty="GENERAL",
        consultation_complete=False,
        session_metadata={}
    )

def extract_latest_response(state: UnifiedConsultationState) -> Optional[str]:
    """Extract the latest AI response from the state."""
    messages = state.get("messages", [])
    for msg in reversed(messages):
        if msg.type == "ai":
            return msg.content
    return None

def get_consultation_summary(state: UnifiedConsultationState) -> dict:
    """Get a summary of the consultation session."""
    messages = state.get("messages", [])
    user_messages = [msg for msg in messages if msg.type == "human"]
    ai_messages = [msg for msg in messages if msg.type == "ai"]

    return {
        "total_exchanges": len(user_messages),
        "current_specialty": state.get("current_specialty", "GENERAL"),
        "session_metadata": state.get("session_metadata", {}),
        "consultation_active": not state.get("consultation_complete", False),
        "last_user_message": user_messages[-1].content if user_messages else None,
        "last_ai_response": ai_messages[-1].content if ai_messages else None
    }

# ================================
# GRAPH FACTORY
# ================================

def get_consultation_graph(continuous: bool = False) -> StateGraph:
    """
    Factory function to get the appropriate graph type.

    Args:
        continuous: If True, returns a graph suitable for continuous CLI interaction
                   If False, returns a graph for single-shot interactions
    """
    if continuous:
        return build_continuous_graph()
    else:
        return build_graph()
