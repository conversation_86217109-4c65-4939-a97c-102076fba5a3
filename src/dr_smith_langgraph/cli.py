import click
import sys
from typing import Dict, Any
from langchain_core.messages import HumanMessage, AIMessage
from .graph import get_consultation_graph, create_initial_state, extract_latest_response, get_consultation_summary

@click.command(help="Interactive Dr<PERSON> unified consultation with streaming responses")
@click.option("--debug", is_flag=True, help="Enable debug mode to show tools usage and internal state")
@click.option("--session-summary", is_flag=True, help="Show session summary at the end")
def main(debug: bool, session_summary: bool) -> None:
    """
    Main CLI entry point for <PERSON><PERSON>'s unified consultation system.
    Features streaming responses and comprehensive medical assistance.
    """
    try:
        # Build the continuous graph for multi-turn conversations
        graph = get_consultation_graph(continuous=True).compile()

        # Initialize conversation state
        state = {
            "messages": [],
            "current_specialty": "GENERAL",
            "consultation_complete": False,
            "session_metadata": {}
        }

        # Welcome message
        print_welcome_message()

        # Main conversation loop
        while True:
            try:
                # Get user input
                user_input = get_user_input()

                # Handle special commands
                if user_input.lower() in {"quit", "exit", "bye"}:
                    handle_session_end(state, session_summary)
                    break
                elif user_input.lower() == "help":
                    show_help_message()
                    continue
                elif user_input.lower() == "summary":
                    show_session_summary(state)
                    continue
                elif user_input.lower() == "clear":
                    state = reset_session_state()
                    click.echo("🔄 Session cleared. Starting fresh consultation.\n")
                    continue

                # Add user message to state
                state["messages"].append(HumanMessage(content=user_input))

                if debug:
                    click.echo(f"🔍 Debug - Processing: {user_input[:50]}{'...' if len(user_input) > 50 else ''}")
                    click.echo(f"🎯 Current specialty: {state.get('current_specialty', 'GENERAL')}")

                # Stream the response
                stream_doctor_response(graph, state, debug)

            except KeyboardInterrupt:
                click.echo("\n\n👋 Consultation interrupted. Take care!")
                break
            except Exception as e:
                handle_error(e, debug)
                continue

    except Exception as e:
        click.echo(f"❌ Failed to start consultation system: {str(e)}")
        sys.exit(1)

def print_welcome_message():
    """Print the welcome message with system capabilities."""
    welcome_text = """
╭─────────────────────────────────────────────────────────────╮
│  👨‍⚕️ Welcome to Dr. Smith's Advanced Consultation System    │
│                                                             │
│  🏥 Specialties:                                           │
│     • Erectile Dysfunction & Sexual Health                 │
│     • Hair Health & Hair Loss Treatment                    │
│     • Weight Management & Metabolic Health                 │
│     • General Medical Guidance                             │
│                                                             │
│  🔧 Enhanced Features:                                     │
│     • Symptom Analysis & Extraction                        │
│     • Psychological Support & Empathy                      │
│     • Safety & Compliance Checking                         │
│     • Personalized Referrals & Resources                   │
│     • Adaptive Communication Style                         │
│                                                             │
│  💡 Commands: 'help', 'summary', 'clear', 'quit'          │
╰─────────────────────────────────────────────────────────────╯
"""
    click.echo(welcome_text)

def get_user_input() -> str:
    """Get user input with a nice prompt."""
    return click.prompt("You", type=str, prompt_suffix=" 💬 ")

def stream_doctor_response(graph, state: Dict[str, Any], debug: bool = False):
    """Stream the doctor's response token by token."""
    click.echo("\n🩺 Dr. Smith: ", nl=False)

    # Store chunks for building complete response
    response_chunks = []
    complete_response = ""
    tools_used = []

    try:
        # Stream events from the graph
        for event in graph.stream(state):
            if debug and "unified_doctor" in event:
                # Track tools being used
                node_data = event["unified_doctor"]
                if hasattr(node_data, 'get') and 'tool_calls' in str(node_data):
                    tools_used.append("Tool call detected")

            # Look for message updates in the event
            if "messages" in event and event["messages"]:
                latest_message = event["messages"][-1]

                # Only stream AI messages
                if hasattr(latest_message, 'type') and latest_message.type == "ai":
                    new_content = latest_message.content

                    # Calculate new chunk to print
                    if new_content != complete_response:
                        if new_content.startswith(complete_response):
                            # Normal incremental update
                            chunk = new_content[len(complete_response):]
                            if chunk:
                                click.echo(chunk, nl=False)
                                response_chunks.append(chunk)
                                complete_response = new_content
                        else:
                            # Content changed significantly, print full new content
                            if complete_response:
                                click.echo("\n[Response updated]\n", nl=False)
                            click.echo(new_content, nl=False)
                            response_chunks = [new_content]
                            complete_response = new_content

        # Update state with the complete conversation
        if "messages" in event:
            state.update(event)

        # Add newlines after response
        click.echo("\n")

        # Show debug information
        if debug:
            show_debug_info(state, tools_used, complete_response)

    except Exception as e:
        click.echo(f"\n❌ Error during streaming: {str(e)}")
        # Fallback: try to get response without streaming
        try:
            result = graph.invoke(state)
            if "messages" in result and result["messages"]:
                latest_msg = result["messages"][-1]
                if latest_msg.type == "ai":
                    click.echo(f"[Fallback response]: {latest_msg.content}\n")
                    state.update(result)
        except Exception as fallback_error:
            click.echo(f"❌ Fallback also failed: {str(fallback_error)}\n")

def show_debug_info(state: Dict[str, Any], tools_used: list, response: str):
    """Show debug information about the current state and processing."""
    click.echo("─" * 60)
    click.echo(f"🔍 Debug Information:")
    click.echo(f"   📊 Current Specialty: {state.get('current_specialty', 'GENERAL')}")
    click.echo(f"   💬 Total Messages: {len(state.get('messages', []))}")
    click.echo(f"   🔧 Tools Detected: {len(tools_used)} tool calls")

    # Show session metadata if available
    metadata = state.get('session_metadata', {})
    if metadata:
        click.echo(f"   📋 Session Data: {metadata}")

    click.echo(f"   📝 Response Length: {len(response)} characters")
    click.echo("─" * 60)

def show_help_message():
    """Display help information about available commands and features."""
    help_text = """
┌─────────────────────────────────────────────────────────────┐
│                     🆘 Help & Commands                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Commands:                                                  │
│    • help     - Show this help message                     │
│    • summary  - Display current session summary            │
│    • clear    - Reset the conversation and start fresh     │
│    • quit     - End the consultation session               │
│                                                             │
│  How to Use:                                               │
│    • Describe your health concerns naturally               │
│    • Ask follow-up questions for clarification             │
│    • Request specific information or resources              │
│                                                             │
│  Sample Questions:                                          │
│    • "I've been experiencing hair loss for 3 months"      │
│    • "I'm struggling with erectile dysfunction"            │
│    • "I need help with a weight loss plan"                │
│    • "What should I know about [condition]?"              │
│                                                             │
│  Privacy: This is a demonstration system. Do not share     │
│           sensitive personal medical information.           │
│                                                             │
└─────────────────────────────────────────────────────────────┘
"""
    click.echo(help_text)

def show_session_summary(state: Dict[str, Any]):
    """Display a summary of the current consultation session."""
    summary = get_consultation_summary(state)

    click.echo("\n📋 Session Summary:")
    click.echo("─" * 40)
    click.echo(f"   Specialty Focus: {summary.get('current_specialty', 'General')}")
    click.echo(f"   Total Exchanges: {summary.get('total_exchanges', 0)}")
    click.echo(f"   Session Active: {'Yes' if summary.get('consultation_active', True) else 'No'}")

    if summary.get('last_user_message'):
        last_msg = summary['last_user_message'][:100] + "..." if len(summary['last_user_message']) > 100 else summary['last_user_message']
        click.echo(f"   Last Question: {last_msg}")

    click.echo("─" * 40)

def handle_session_end(state: Dict[str, Any], show_summary: bool):
    """Handle the end of a consultation session."""
    click.echo("\n👋 Thank you for using Dr. Smith's consultation system!")

    if show_summary:
        show_session_summary(state)

    # Provide closing resources
    click.echo("\n🏥 Remember:")
    click.echo("   • This system provides general guidance only")
    click.echo("   • Always consult healthcare professionals for serious concerns")
    click.echo("   • For specialized care, consider the Gustorx referrals provided")
    click.echo("\n💙 Take care of your health!")

def reset_session_state() -> Dict[str, Any]:
    """Reset the session state for a fresh consultation."""
    return {
        "messages": [],
        "current_specialty": "GENERAL",
        "consultation_complete": False,
        "session_metadata": {}
    }

def handle_error(error: Exception, debug: bool):
    """Handle errors gracefully with appropriate user feedback."""
    if debug:
        click.echo(f"\n❌ Debug - Error details: {str(error)}")

    click.echo("\n⚠️  I apologize, but I encountered a technical issue.")
    click.echo("    Let's try continuing our conversation. Please rephrase your question.")
    click.echo("    If the problem persists, try the 'clear' command to start fresh.\n")

# Additional utility functions for enhanced CLI experience
def format_specialty_name(specialty: str) -> str:
    """Format specialty names for display."""
    specialty_display = {
        "ERECTILE_DYSFUNCTION": "Erectile Dysfunction",
        "HAIR_HEALTH": "Hair Health",
        "WEIGHT_LOSS": "Weight Management",
        "GENERAL": "General Medicine"
    }
    return specialty_display.get(specialty, specialty)

# Entry point
if __name__ == "__main__":
    main()
