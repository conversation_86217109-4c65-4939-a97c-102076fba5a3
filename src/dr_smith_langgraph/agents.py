from __future__ import annotations
from typing import Literal, List, Optional, Dict, Any
from langchain_openai import Chat<PERSON><PERSON>A<PERSON>
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.tools import tool
from langgraph.prebuilt import create_react_agent
from pydantic import BaseModel
import json

from .config import OPENAI_API_KEY, MODEL_NAME, TEMPERATURE, GUSTORX_URLS
from .prompts import (
    UNIFIED_DOCTOR_SYSTEM_PROMPT,
    SYMPTOM_EXTRACTION_PROMPT,
    PSYCHOLOGICAL_ASSESSMENT_PROMPT,
    EMPATHETIC_SUPPORT_PROMPT,
    SAFETY_COMPLIANCE_PROMPT,
    LANGUAGE_TONE_ADJUSTMENT_PROMPT,
    REFERRAL_RESOURCES_PROMPT,
    SPECIALTY_KNOWLEDGE
)

# Initialize LLM
llm = ChatOpenAI(
    openai_api_key=OPENAI_API_KEY,
    model_name=MODEL_NAME,
    temperature=TEMPERATURE
)

# Pydantic models for structured outputs
class SymptomData(BaseModel):
    primary_symptom: Optional[str] = None
    duration: Optional[str] = None
    severity: Optional[str] = None
    associated_symptoms: Optional[List[str]] = None
    triggers: Optional[str] = None
    impact: Optional[str] = None

class PsychologicalContext(BaseModel):
    emotional_state: str
    distress_level: str
    sensitive_topic: bool
    psychological_needs: List[str]
    communication_barriers: Optional[str] = None
    crisis_indicators: Optional[str] = None

class SafetyAssessment(BaseModel):
    is_safe: bool
    emergency_flag: bool
    issues_found: Optional[List[str]] = None
    corrected_response: Optional[str] = None
    urgent_care_needed: bool = False

class ReferralResource(BaseModel):
    primary_referral: str
    additional_resources: Optional[List[str]] = None
    timeline: Optional[str] = None
    preparation: Optional[str] = None
    cost_considerations: Optional[str] = None

# ================================
# SPECIALIZED TOOLS
# ================================

@tool
def extract_symptoms(user_text: str) -> str:
    """Extract structured symptom information from patient's description of their medical concerns."""
    try:
        structured_llm = llm.with_structured_output(SymptomData)
        result = structured_llm.invoke([
            SystemMessage(content=SYMPTOM_EXTRACTION_PROMPT),
            HumanMessage(content=f"Patient message: {user_text}")
        ])
        return json.dumps(result.dict(), indent=2)
    except Exception as e:
        return f"Error extracting symptoms: {str(e)}"

@tool
def assess_psychological_state(user_text: str, conversation_history: str = "") -> str:
    """Analyze patient's emotional state and psychological needs for providing appropriate support."""
    try:
        structured_llm = llm.with_structured_output(PsychologicalContext)
        context = f"Current message: {user_text}\n\nConversation history: {conversation_history}"
        result = structured_llm.invoke([
            SystemMessage(content=PSYCHOLOGICAL_ASSESSMENT_PROMPT),
            HumanMessage(content=context)
        ])
        return json.dumps(result.dict(), indent=2)
    except Exception as e:
        return f"Error assessing psychological state: {str(e)}"

@tool
def add_empathetic_support(medical_response: str, psychological_context: str, patient_concern: str) -> str:
    """Enhance medical responses with appropriate emotional support and empathy."""
    try:
        context = f"""
Medical Response to Enhance: {medical_response}

Psychological Context: {psychological_context}

Patient's Main Concern: {patient_concern}

Please enhance this response with appropriate empathy while preserving all medical information.
"""
        result = llm.invoke([
            SystemMessage(content=EMPATHETIC_SUPPORT_PROMPT),
            HumanMessage(content=context)
        ])
        return result.content
    except Exception as e:
        return f"Error adding empathetic support: {str(e)}"

@tool
def check_safety_compliance(medical_response: str, patient_context: str) -> str:
    """Review medical advice for safety, accuracy, and compliance with healthcare standards."""
    try:
        structured_llm = llm.with_structured_output(SafetyAssessment)
        review_context = f"""
Medical Response to Review: {medical_response}

Patient Context: {patient_context}

Please review for safety, accuracy, and compliance.
"""
        result = structured_llm.invoke([
            SystemMessage(content=SAFETY_COMPLIANCE_PROMPT),
            HumanMessage(content=review_context)
        ])

        if result.emergency_flag or result.urgent_care_needed:
            return f"⚠️ URGENT: {result.corrected_response or medical_response}"
        elif not result.is_safe and result.corrected_response:
            return result.corrected_response
        else:
            return medical_response
    except Exception as e:
        return f"Error in safety check: {str(e)}"

@tool
def adjust_language_tone(response: str, patient_emotional_state: str, complexity_preference: str = "simple") -> str:
    """Adjust medical language and tone for optimal patient understanding and emotional appropriateness."""
    try:
        adjustment_context = f"""
Response to adjust: {response}

Patient's emotional state: {patient_emotional_state}
Complexity preference: {complexity_preference}

Please adjust the language and tone while preserving all medical accuracy.
"""
        result = llm.invoke([
            SystemMessage(content=LANGUAGE_TONE_ADJUSTMENT_PROMPT),
            HumanMessage(content=adjustment_context)
        ])
        return result.content
    except Exception as e:
        return f"Error adjusting language: {str(e)}"

@tool
def provide_referral_resources(condition: str, patient_context: str, severity: str = "moderate") -> str:
    """Provide personalized referral recommendations and helpful resources for the patient's condition."""
    try:
        # Determine specialty and appropriate Gustorx URL
        specialty_mapping = {
            "erectile dysfunction": "ERECTILE_DYSFUNCTION",
            "ed": "ERECTILE_DYSFUNCTION",
            "hair loss": "HAIR_HEALTH",
            "hair": "HAIR_HEALTH",
            "balding": "HAIR_HEALTH",
            "weight": "WEIGHT_LOSS",
            "obesity": "WEIGHT_LOSS"
        }

        specialty = None
        for key, spec in specialty_mapping.items():
            if key in condition.lower():
                specialty = spec
                break

        referral_context = f"""
Patient's condition: {condition}
Patient context: {patient_context}
Severity level: {severity}
Identified specialty: {specialty}

Available Gustorx URLs:
- Hair Loss: {GUSTORX_URLS['hair_loss']}
- Erectile Dysfunction: {GUSTORX_URLS['erectile_dysfunction']}
- Weight Loss: {GUSTORX_URLS['weight_loss']}

Please provide comprehensive referral and resource recommendations.
"""
        result = llm.invoke([
            SystemMessage(content=REFERRAL_RESOURCES_PROMPT),
            HumanMessage(content=referral_context)
        ])
        return result.content
    except Exception as e:
        return f"Error providing referrals: {str(e)}"

@tool
def get_specialty_knowledge(specialty: str, query_type: str = "general") -> str:
    """Access specialized medical knowledge for specific conditions and treatment approaches."""
    try:
        specialty_upper = specialty.upper()
        if specialty_upper in SPECIALTY_KNOWLEDGE:
            knowledge = SPECIALTY_KNOWLEDGE[specialty_upper]

            knowledge_text = f"""
Specialty: {specialty}
Query Type: {query_type}

Available Knowledge:
"""
            for key, value in knowledge.items():
                if isinstance(value, list):
                    knowledge_text += f"- {key.title()}: {', '.join(value)}\n"
                else:
                    knowledge_text += f"- {key.title()}: {value}\n"

            return knowledge_text
        else:
            return f"No specialized knowledge available for {specialty}"
    except Exception as e:
        return f"Error accessing specialty knowledge: {str(e)}"

# ================================
# UNIFIED DOCTOR AGENT
# ================================

# Create the unified Dr. Smith agent with all tools
unified_doctor_tools = [
    extract_symptoms,
    assess_psychological_state,
    add_empathetic_support,
    check_safety_compliance,
    adjust_language_tone,
    provide_referral_resources,
    get_specialty_knowledge
]

# Create the unified agent
unified_doctor_agent = create_react_agent(
    llm,
    tools=unified_doctor_tools,
    prompt=UNIFIED_DOCTOR_SYSTEM_PROMPT
)

# ================================
# HELPER FUNCTIONS
# ================================

def classify_medical_specialty(user_text: str) -> Literal["ERECTILE_DYSFUNCTION", "HAIR_HEALTH", "WEIGHT_LOSS", "GENERAL"]:
    """Quick classification of medical specialty for routing purposes."""
    text_lower = user_text.lower()

    # REORDER: Check weight keywords FIRST (they were being missed)
    weight_keywords = ["weight", "obesity", "overweight", "diet", "lose weight", "fat", "metabolism"]
    if any(keyword in text_lower for keyword in weight_keywords):
        return "WEIGHT_LOSS"

    # Hair keywords
    hair_keywords = ["hair", "bald", "balding", "alopecia", "thinning", "hairline", "scalp"]
    if any(keyword in text_lower for keyword in hair_keywords):
        return "HAIR_HEALTH"

    # ED keywords (check last to avoid conflicts)
    ed_keywords = ["erectile", "ed", "impotence", "sexual", "erection", "performance", "intimacy"]
    if any(keyword in text_lower for keyword in ed_keywords):
        return "ERECTILE_DYSFUNCTION"

    return "GENERAL"

def extract_user_messages(messages: list) -> str:
    """Extract and concatenate user messages for context."""
    user_texts = []
    for msg in messages:
        if hasattr(msg, 'type') and msg.type == "human":
            user_texts.append(msg.content)
    return " | ".join(user_texts)

def get_conversation_summary(messages: list, max_length: int = 500) -> str:
    """Get a concise summary of the conversation for tool context."""
    if not messages:
        return "No previous conversation."

    summary = []
    for msg in messages[-6:]:  # Last 6 messages for context
        role = "Patient" if msg.type == "human" else "Dr. Smith"
        content = msg.content[:100] + "..." if len(msg.content) > 100 else msg.content
        summary.append(f"{role}: {content}")

    full_summary = " | ".join(summary)
    if len(full_summary) > max_length:
        return full_summary[:max_length] + "..."
    return full_summary
