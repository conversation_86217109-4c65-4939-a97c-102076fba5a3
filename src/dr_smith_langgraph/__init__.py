"""
dr_smith_langgraph
------------------
Unified LangGraph multi-agent medical consultation system for Dr<PERSON> <PERSON>.

This package provides a comprehensive medical consultation bot with:
- Unified agent architecture with specialized tools
- Symptom extraction and analysis
- Psychological support and empathy
- Safety and compliance checking
- Language and tone adaptation
- Personalized referrals and resources
- Streaming consultation interface

Main Components:
- UnifiedDoctorAgent: Core consultation agent with all tools
- ConsultationGraph: LangGraph workflow for medical conversations
- CLI: Interactive streaming consultation interface
- Tools: Specialized medical consultation tools
"""

__version__ = "1.0.0"
__author__ = "Dr. <PERSON> LangGraph Team"
__description__ = "Unified medical consultation system with LangGraph"

# ================================
# CORE IMPORTS
# ================================

from .config import (
    # Configuration
    OPENAI_API_KEY,
    MODEL_NAME,
    TEMPERATURE,
    GUSTORX_URLS,
    TOOL_CONFIG,
    SPECIALTY_CONFIG,

    # Utility functions
    get_llm_config,
    get_tool_config,
    is_tool_enabled,
    get_specialty_config,
    should_trigger_psychological_support,
    is_sensitive_specialty,
    get_referral_url,
    validate_configuration,
    get_environment_info,

    # Paths
    BASE_DIR,
    PROJECT_ROOT
)

from .agents import (
    # Main unified agent
    unified_doctor_agent,

    # Individual tools
    extract_symptoms,
    assess_psychological_state,
    add_empathetic_support,
    check_safety_compliance,
    adjust_language_tone,
    provide_referral_resources,
    get_specialty_knowledge,

    # Helper functions
    classify_medical_specialty,
    extract_user_messages,
    get_conversation_summary,

    # Core LLM
    llm
)

from .graph import (
    # State management
    UnifiedConsultationState,

    # Graph builders
    build_graph,
    build_continuous_graph,
    get_consultation_graph,

    # Node functions
    unified_doctor_node,
    session_initializer_node,

    # Utility functions
    create_initial_state,
    extract_latest_response,
    get_consultation_summary
)

from .prompts import (
    # Main system prompt
    UNIFIED_DOCTOR_SYSTEM_PROMPT,

    # Tool prompts
    SYMPTOM_EXTRACTION_PROMPT,
    PSYCHOLOGICAL_ASSESSMENT_PROMPT,
    EMPATHETIC_SUPPORT_PROMPT,
    SAFETY_COMPLIANCE_PROMPT,
    LANGUAGE_TONE_ADJUSTMENT_PROMPT,
    REFERRAL_RESOURCES_PROMPT,

    # Knowledge base
    SPECIALTY_KNOWLEDGE
)

# ================================
# HIGH-LEVEL API
# ================================

class DrSmithConsultation:
    """
    High-level API for Dr. Smith's consultation system.

    This class provides a simple interface for programmatic access
    to the consultation system without using the CLI.
    """

    def __init__(self, continuous: bool = True, debug: bool = False):
        """
        Initialize a consultation session.

        Args:
            continuous: Whether to use continuous conversation mode
            debug: Enable debug mode for detailed logging
        """
        self.graph = get_consultation_graph(continuous=continuous).compile()
        self.debug = debug
        self.state = {
            "messages": [],
            "current_specialty": "GENERAL",
            "consultation_complete": False,
            "session_metadata": {}
        }

    def consult(self, user_message: str) -> str:
        """
        Get a consultation response for a user message.

        Args:
            user_message: The user's health concern or question

        Returns:
            Dr. Smith's response as a string
        """
        from langchain_core.messages import HumanMessage

        try:
            # Handle empty or whitespace-only input
            if not user_message.strip():
                return "I apologize, I couldn't process empty input. Please try again."

            # Add user message to state
            self.state["messages"].append(HumanMessage(content=user_message))

            # Get response from graph
            result = self.graph.invoke(self.state)

            # Update state
            self.state.update(result)

            # Extract and return latest response
            return extract_latest_response(self.state) or "I apologize, I couldn't process your request."

        except Exception as e:
            # Graceful error handling
            return "I apologize, I couldn't process your request. Please try again."

    def get_session_info(self) -> dict:
        """Get information about the current consultation session."""
        return get_consultation_summary(self.state)

    def reset_session(self):
        """Reset the consultation session to start fresh."""
        self.state = {
            "messages": [],
            "current_specialty": "GENERAL",
            "consultation_complete": False,
            "session_metadata": {}
        }

    def get_current_specialty(self) -> str:
        """Get the currently detected medical specialty."""
        return self.state.get("current_specialty", "GENERAL")

# ================================
# CONVENIENCE FUNCTIONS
# ================================

def create_consultation_session(continuous: bool = True, debug: bool = False) -> DrSmithConsultation:
    """
    Create a new consultation session.

    Args:
        continuous: Whether to enable continuous conversation mode
        debug: Whether to enable debug logging

    Returns:
        A new DrSmithConsultation instance
    """
    return DrSmithConsultation(continuous=continuous, debug=debug)

def quick_consult(user_message: str, debug: bool = False) -> str:
    """
    Get a quick consultation response without maintaining session state.

    Args:
        user_message: The user's health concern
        debug: Enable debug mode

    Returns:
        Dr. Smith's response
    """
    session = create_consultation_session(continuous=False, debug=debug)
    return session.consult(user_message)

def validate_system() -> bool:
    """
    Validate that the system is properly configured and ready to use.

    Returns:
        True if system is ready, False otherwise
    """
    try:
        # Validate configuration
        if not validate_configuration():
            return False

        # Test basic graph compilation
        graph = get_consultation_graph(continuous=False)
        graph.compile()

        # Test agent creation
        if unified_doctor_agent is None:
            return False

        return True

    except Exception as e:
        print(f"System validation failed: {e}")
        return False

def get_system_info() -> dict:
    """
    Get comprehensive information about the system configuration.

    Returns:
        Dictionary with system information
    """
    return {
        "version": __version__,
        "environment": get_environment_info(),
        "tools_available": [
            "extract_symptoms",
            "assess_psychological_state",
            "add_empathetic_support",
            "check_safety_compliance",
            "adjust_language_tone",
            "provide_referral_resources",
            "get_specialty_knowledge"
        ],
        "specialties_supported": list(SPECIALTY_CONFIG.keys()),
        "gustorx_integrations": list(GUSTORX_URLS.keys()),
        "system_ready": validate_system()
    }

# ================================
# SPECIALTY-SPECIFIC HELPERS
# ================================

def consult_ed_specialist(user_message: str) -> str:
    """Quick consultation with focus on erectile dysfunction."""
    session = create_consultation_session()
    session.state["current_specialty"] = "ERECTILE_DYSFUNCTION"
    return session.consult(user_message)

def consult_hair_specialist(user_message: str) -> str:
    """Quick consultation with focus on hair health."""
    session = create_consultation_session()
    session.state["current_specialty"] = "HAIR_HEALTH"
    return session.consult(user_message)

def consult_weight_specialist(user_message: str) -> str:
    """Quick consultation with focus on weight management."""
    session = create_consultation_session()
    session.state["current_specialty"] = "WEIGHT_LOSS"
    return session.consult(user_message)

# ================================
# EXPORTS
# ================================

__all__ = [
    # Version and metadata
    "__version__",
    "__author__",
    "__description__",

    # High-level API
    "DrSmithConsultation",
    "create_consultation_session",
    "quick_consult",

    # System utilities
    "validate_system",
    "get_system_info",

    # Specialty helpers
    "consult_ed_specialist",
    "consult_hair_specialist",
    "consult_weight_specialist",

    # Core components (for advanced usage)
    "unified_doctor_agent",
    "get_consultation_graph",
    "UnifiedConsultationState",

    # Configuration
    "GUSTORX_URLS",
    "SPECIALTY_CONFIG",
    "get_llm_config",

    # Tools (for custom implementations)
    "extract_symptoms",
    "assess_psychological_state",
    "add_empathetic_support",
    "check_safety_compliance",
    "adjust_language_tone",
    "provide_referral_resources",
    "get_specialty_knowledge"
]

# ================================
# PACKAGE INITIALIZATION
# ================================

# Validate system on import
_system_ready = validate_system()
if not _system_ready:
    import warnings
    warnings.warn(
        "Dr. Smith consultation system validation failed. "
        "Please check your configuration and dependencies.",
        UserWarning
    )

# Display system info in debug mode
if get_environment_info().get("debug_mode") == "true":
    print("Dr. Smith LangGraph System Initialized")
    print(f"Version: {__version__}")
    print(f"System Ready: {_system_ready}")
