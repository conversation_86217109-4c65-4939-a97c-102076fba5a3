# 🩺 <PERSON><PERSON> LangGraph Consultation System

A unified, AI-powered medical consultation system built with LangGraph that provides empathetic, specialized healthcare guidance with advanced tool integration.

## ✨ Features

### 🔧 **Unified Agent Architecture**
- Single intelligent agent with specialized tools (no complex multi-agent orchestration)
- Dynamic prompt adaptation based on medical specialty
- Streamlined conversation flow with memory continuity

### 🏥 **Medical Specializations**
- **Erectile Dysfunction** - Sensitive, empathetic sexual health guidance
- **Hair Health** - Comprehensive hair loss assessment and treatment options
- **Weight Management** - Holistic, sustainable weight loss approaches
- **General Medicine** - Broad medical guidance and specialist referrals

### 🛠️ **Advanced Tools**
- **Symptom Extraction** - Structured clinical data parsing
- **Psychological Support** - Emotional intelligence and empathy enhancement
- **Safety & Compliance** - Medical accuracy and safety validation
- **Language Adaptation** - Communication style adjustment for accessibility
- **Smart Referrals** - Personalized resource recommendations with Gustorx integration
- **Specialty Knowledge** - Domain-specific medical knowledge access

### 💬 **Interactive Experience**
- **Streaming responses** - Real-time token-by-token output
- **Memory persistence** - Multi-turn conversation continuity
- **Debug mode** - Detailed system insights for development
- **Session management** - Summary, reset, and help commands

## 🚀 Quick Start

### 1. **Installation**

```
# Clone the repository
git clone
cd dr_smith_langgraph

# Create virtual environment
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install the package
pip install -e .
```

### 2. **Configuration**

Create a `.env` file in the project root:

```
# Required
OPENAI_API_KEY=sk-your-openai-api-key-here

# Optional customization
MODEL_NAME=gpt-4o-mini
TEMPERATURE=0.3
```

### 3. **Run the Consultation**

```
# Start interactive consultation
dr-smith

# With debug information
dr-smith --debug

# Show session summary at end
dr-smith --session-summary
```

## 💻 CLI Usage

### **Interactive Commands**
- `help` - Show available commands and usage guide
- `summary` - Display current session statistics
- `clear` - Reset conversation and start fresh
- `quit` - End consultation session

### **Example Session**
```
👋 Streaming mode — type 'quit' to exit.

You 💬 I've been losing hair for about 6 months now

🩺 Dr. Smith: I understand your concern about hair loss, and I want you to know
that you're not alone in this experience. Hair loss affects millions of people
and can be emotionally challenging...

[Response streams in real-time]

You 💬 What treatment options do I have?

🩺 Dr. Smith: There are several effective treatment approaches we can explore
for your hair loss...
```

## 🐍 Programmatic API

### **Simple Usage**
```
from dr_smith_langgraph import quick_consult

# Get a quick consultation response
response = quick_consult("I'm experiencing erectile dysfunction")
print(response)
```

### **Session-Based Usage**
```
from dr_smith_langgraph import create_consultation_session

# Create a persistent session
session = create_consultation_session()

# Multi-turn conversation
response1 = session.consult("I've been losing weight but it's not sustainable")
response2 = session.consult("What diet approach would you recommend?")
response3 = session.consult("How do I stay motivated long-term?")

# Get session information
info = session.get_session_info()
print(f"Current specialty: {session.get_current_specialty()}")
print(f"Total exchanges: {info['total_exchanges']}")
```

### **Specialty-Specific Consultations**
```
from dr_smith_langgraph import (
    consult_ed_specialist,
    consult_hair_specialist,
    consult_weight_specialist
)

# Direct specialty consultation
ed_response = consult_ed_specialist("I'm having performance anxiety")
hair_response = consult_hair_specialist("My hairline is receding")
weight_response = consult_weight_specialist("I need a sustainable diet plan")
```

## 🏗️ Architecture

### **System Components**
```
User Input
    ↓
Unified Doctor Agent
    ├── Symptom Extraction Tool
    ├── Psychological Assessment Tool
    ├── Empathy Enhancement Tool
    ├── Safety Compliance Tool
    ├── Language Adjustment Tool
    ├── Referral Resources Tool
    └── Specialty Knowledge Tool
    ↓
Streaming Response
```

### **Key Benefits**
- **Single LLM Call**: Cost-effective with one API call per turn
- **Tool-Based Intelligence**: Agent decides when/how to use tools
- **Memory Continuity**: Full conversation context maintained
- **Safety First**: Built-in medical accuracy and compliance checking
- **Empathetic Care**: Psychological support for sensitive topics

## 🧪 Development

### **Run Tests**
```
# Install dev dependencies
pip install -e .[dev]

# Run all tests
pytest

# Run with coverage
pytest --cov=src/dr_smith_langgraph
```

### **Code Quality**
```
# Format code
ruff format src/ tests/

# Lint code
ruff check src/ tests/

# Type checking (if mypy installed)
mypy src/
```

### **System Validation**
```
from dr_smith_langgraph import validate_system, get_system_info

# Check system health
is_ready = validate_system()
print(f"System ready: {is_ready}")

# Get detailed system info
info = get_system_info()
print(f"Tools available: {len(info['tools_available'])}")
print(f"Specialties: {info['specialties_supported']}")
```

## ⚙️ Configuration

### **Environment Variables**
```
# Core configuration
OPENAI_API_KEY=sk-...                    # Required: OpenAI API key
MODEL_NAME=gpt-4o-mini                   # Optional: Model selection
TEMPERATURE=0.3                          # Optional: Response creativity

# Tool configuration
ENABLE_SYMPTOM_EXTRACTION=true           # Enable symptom parsing
ENABLE_PSYCHOLOGICAL_SUPPORT=true        # Enable empathy features
ENABLE_SAFETY_CHECKS=true                # Enable safety validation
ENABLE_LANGUAGE_ADJUSTMENT=true          # Enable communication adaptation
ENABLE_REFERRALS=true                    # Enable resource suggestions

# Development settings
DEBUG_LOGGING=false                      # Enable detailed logging
LOG_TOOL_USAGE=false                     # Log tool invocations
```

### **Specialty Customization**
Each medical specialty can be configured for:
- Psychological support requirements
- Sensitivity level handling
- Referral thresholds
- Assessment factors
- Treatment approaches

## 🔗 Integrations

### **Gustorx Referrals**
Automatic integration with Gustorx consultation platform:
- **Hair Loss**: `https://app.gustorx.com/start-hl-consult`
- **Erectile Dysfunction**: `https://app.gustorx.com/start-ed-consult`
- **Weight Loss**: `https://app.gustorx.com/start-wl-consult`

## 📋 Project Structure

```
dr_smith_langgraph/
├── src/dr_smith_langgraph/
│   ├── __init__.py          # Package exports and high-level API
│   ├── agents.py            # Unified agent and specialized tools
│   ├── cli.py               # Interactive streaming CLI
│   ├── config.py            # Configuration management
│   ├── graph.py             # LangGraph workflow definition
│   └── prompts.py           # System prompts and instructions
├── tests/
│   └── test_unified_system.py  # Comprehensive test suite
├── .env.example             # Environment template
├── README.md               # This documentation
└── pyproject.toml          # Project configuration
```

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** changes (`git commit -m 'Add amazing feature'`)
4. **Push** to branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

## ⚠️ Important Disclaimers

- **Educational Purpose**: This system is for demonstration and educational use
- **Not Medical Advice**: Responses are AI-generated and not professional medical advice
- **Consult Professionals**: Always consult qualified healthcare providers for medical concerns
- **Privacy**: Do not share sensitive personal medical information in demonstrations

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

### **Common Issues**

**"Command not found: dr-smith"**
```
# Ensure package is installed in editable mode
pip install -e .

# Check if CLI is registered
dr-smith --help
```

**"OpenAI API Error"**
```
# Verify API key in .env file
echo $OPENAI_API_KEY

# Test API connectivity
python -c "from dr_smith_langgraph import validate_system; print(validate_system())"
```

**"Import Error"**
```
# Reinstall with dependencies
pip install -e .[dev]

# Check Python version (requires 3.10+)
python --version
```

### **Getting Help**
- 📖 Read the documentation thoroughly
- 🧪 Run the test suite to verify setup
- 🔍 Use `--debug` mode for detailed information
- 💬 Check the Issues page for common problems

---

**Built with ❤️ using LangGraph, LangChain, and OpenAI**
